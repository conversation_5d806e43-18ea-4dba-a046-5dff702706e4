import os
from dotenv import load_dotenv
from PIL import Image
import io
import base64

load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GROQ_API_KEY:
    raise ValueError("GROQ_API_KEY environment variable is not set.")

# Load and resize the image
image_path = "confident-young-woman-with-acne-close-up.jpg"
try:
    # Open the image
    img = Image.open(image_path)
    
    # Resize the image while maintaining aspect ratio
    max_size = (800, 800)  # Adjust dimensions as needed
    img.thumbnail(max_size, Image.LANCZOS)
    
    # Save to a bytes buffer
    buffer = io.BytesIO()
    img.save(buffer, format="JPEG", quality=85)  # Adjust quality as needed
    buffer.seek(0)
    
    # Encode to base64
    encoded_image = base64.b64encode(buffer.read()).decode("utf-8")
except FileNotFoundError:
    raise FileNotFoundError(f"Image file '{image_path}' not found.")

from groq import Groq

client = Groq()
query = ""
model="meta-llama/llama-4-maverick-17b-128e-instruct"
message=[
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": query
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encoded_image}"
                }
            }
        ]
    }
]
chat_completion = client.chat.completions.create(
    model=model,
    messages=message
)

print(chat_completion.choices[0].message)

