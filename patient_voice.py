#Setting up Audio recorder (ffmpeg & portaudio)
import logging 
import speech_recognition as sr
from pydub import AudioSegment
from io import BytesIO

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def record_audio(file_path, timeout=20, phrase_time_limit=None):
    """Simplified function to record audio from the microphone and save it as an MP3 file.

    Args:
        file_path (str): The path where the recorded audio will be saved.
        timeout (int): Maximum time to wait for a phase to start in seconds.
        phrase_time_limit (int): Maximum time for the phrase to be recorded in seconds. Defaults to None.
    """
    recognizer = sr.Recognizer()

    try:
        with sr.Microphone() as source:
            logging.info("Adjust for ambient noise...")
            recognizer.adjust_for_ambient_noise(source, duration=1)
            logging.info("Start speaking...")

            audio_data = recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
            logging.info("Recording complete.")

            wav_data = audio_data.get_wav_data()
            audio_segment = AudioSegment.from_wav(BytesIO(wav_data))
            audio_segment.export(file_path, format="mp3", bitrate="128k")

            logging.info(f"Audio saved to {file_path}")
    except sr.WaitTimeoutError:
        logging.error("Recording timed out. Please try again.")
    except Exception as e:
        logging.error(f"An error occurred while recording audio: {e}")

#record_audio(file_path="patient_voice.mp3")
audio_filepath = "patient_voice.mp3"
record_audio(file_path=audio_filepath)

# Setup speech to text-SST-model for transcription
import os
from groq import Groq
from dotenv import load_dotenv

load_dotenv()

GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GROQ_API_KEY:
    raise ValueError("GROQ_API_KEY environment variable is not set.")
from groq import Groq
client = Groq(api_key=GROQ_API_KEY)
stt_model = "whisper-large-v3"
audio_file = open(audio_filepath, "rb")
transcription = client.audio.transcriptions.create(
    model=stt_model,
    file=audio_file,
    language="en"
)

print(transcription.text)


