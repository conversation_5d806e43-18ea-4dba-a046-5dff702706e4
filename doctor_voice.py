# Setup Text to Speech-TTS-model (gTTS & ElevenLabs)
import os 
from gtts import gTTS

def text_to_speech_with_gtts(imput_text, output_filepath):
    language = 'en'

    audioobj= gTTS(text=input_text, lang=language, slow=False)
    audioobj.save(output_filepath)

input_text = "Hello, this is a test of the text to speech functionality."
text_to_speech_with_gtts(input_text, output_filepath="output.mp3")

#Setup Text to Speech-TTS-model (ElevenLabs)
import os
import elevenlabs
from elevenlabs.client import ElevenLabs


def text_to_speech_with_elevenlabs(input_text, output_filepath):
    pass

#Use Model for Text output to voice